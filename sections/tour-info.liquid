{{ 'component-tour-info.css' | asset_url | stylesheet_tag }}

<div class="tour-info-section section-{{ section.id }}-padding">
  <div class="page-width">
    <!-- Tour Highlights -->
    <div class="tour-highlights">
      <h3 class="tour-section-title">{{ section.settings.highlights_title | default: 'Tour Highlights' }}</h3>
      <div class="highlights-grid">
        {% for block in section.blocks %}
          {% if block.type == 'highlight' %}
            <div class="highlight-item" {{ block.shopify_attributes }}>
              <div class="highlight-icon">
                {{ block.settings.icon }}
              </div>
              <div class="highlight-content">
                <h4>{{ block.settings.title }}</h4>
                <p>{{ block.settings.description }}</p>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
    
    <!-- What's Included Section -->
    <div class="tour-included">
      <h3 class="tour-section-title">{{ section.settings.included_title | default: "What's Included" }}</h3>
      <div class="included-list">
        {% for block in section.blocks %}
          {% if block.type == 'included_item' %}
            <div class="included-item" {{ block.shopify_attributes }}>
              <svg class="included-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="20,6 9,17 4,12"></polyline>
              </svg>
              <span>{{ block.settings.text }}</span>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
    
    <!-- Tour Itinerary Section -->
    <div class="tour-itinerary">
      <h3 class="tour-section-title">{{ section.settings.itinerary_title | default: 'Tour Itinerary' }}</h3>
      <div class="itinerary-timeline">
        {% for block in section.blocks %}
          {% if block.type == 'itinerary_day' %}
            <div class="itinerary-day" {{ block.shopify_attributes }}>
              <div class="day-number">{{ block.settings.day_number }}</div>
              <div class="day-content">
                <h4>{{ block.settings.day_title }}</h4>
                <p>{{ block.settings.day_description }}</p>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Tour Information",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "highlights_title",
      "label": "Highlights Title",
      "default": "Tour Highlights"
    },
    {
      "type": "text",
      "id": "included_title",
      "label": "What's Included Title",
      "default": "What's Included"
    },
    {
      "type": "text",
      "id": "itinerary_title",
      "label": "Itinerary Title",
      "default": "Tour Itinerary"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "highlight",
      "name": "Tour Highlight",
      "settings": [
        {
          "type": "html",
          "id": "icon",
          "label": "Icon SVG",
          "default": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"><path d=\"M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z\"></path><circle cx=\"12\" cy=\"10\" r=\"3\"></circle></svg>"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Scenic Mountain Routes"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "Explore breathtaking mountain landscapes and hidden gems"
        }
      ]
    },
    {
      "type": "included_item",
      "name": "Included Item",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Item Text",
          "default": "Transportation in 4WD vehicle"
        }
      ]
    },
    {
      "type": "itinerary_day",
      "name": "Itinerary Day",
      "settings": [
        {
          "type": "text",
          "id": "day_number",
          "label": "Day Number",
          "default": "1"
        },
        {
          "type": "text",
          "id": "day_title",
          "label": "Day Title",
          "default": "Arrival & Tbilisi Exploration"
        },
        {
          "type": "textarea",
          "id": "day_description",
          "label": "Day Description",
          "default": "Arrive in Tbilisi, explore the old town and traditional sulfur baths"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Tour Information",
      "blocks": [
        {
          "type": "highlight",
          "settings": {
            "title": "Scenic Mountain Routes",
            "description": "Explore breathtaking mountain landscapes and hidden gems"
          }
        },
        {
          "type": "highlight",
          "settings": {
            "icon": "<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\"><path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"></path></svg>",
            "title": "Expert Local Guide",
            "description": "Professional guides with deep local knowledge"
          }
        },
        {
          "type": "included_item",
          "settings": {
            "text": "Transportation in 4WD vehicle"
          }
        },
        {
          "type": "included_item",
          "settings": {
            "text": "Accommodation (4 nights)"
          }
        },
        {
          "type": "itinerary_day",
          "settings": {
            "day_number": "1",
            "day_title": "Arrival & Tbilisi Exploration",
            "day_description": "Arrive in Tbilisi, explore the old town and traditional sulfur baths"
          }
        }
      ]
    }
  ]
}
{% endschema %}

<style>
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
</style>
