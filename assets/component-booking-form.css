/* Professional Booking Form Styles */
.product-with-booking {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
  align-items: start;
}

.product__booking-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0;
  align-self: start;
}

.booking-form-wrapper {
  position: sticky;
  top: 2rem;
  z-index: 10;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 20px;
  box-shadow:
    0 20px 40px -12px rgba(0, 0, 0, 0.12),
    0 8px 16px -4px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
  max-width: 400px;
  margin: 0 auto 2rem auto;

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.booking-form-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 12px 20px -4px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.booking-form-container {
  padding: 2.5rem 2rem;
  position: relative;
}

.booking-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 20px 20px 0 0;
}

.booking-form {
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
}

.booking-form-header {
  margin-bottom: 2.5rem;
  text-align: center;
  padding-bottom: 2rem;
  border-bottom: 2px solid rgba(59, 130, 246, 0.1);
  position: relative;
}

.booking-form-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.booking-price {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.price-label {
  font-size: 0.8125rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  opacity: 0.8;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: 900;
  color: #1e293b;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 0.9;
  position: relative;
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.booking-field {
  display: flex;
  flex-direction: column;
  gap: 0.875rem;
  position: relative;
}

.booking-label {
  font-size: 0.9375rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
}

.booking-label::before {
  content: '';
  width: 4px;
  height: 4px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 50%;
  flex-shrink: 0;
}

.booking-input,
.booking-select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px;
  font-size: 0.9375rem;
  font-weight: 500;
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(4px);
}

.booking-input:focus,
.booking-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.12),
    0 8px 16px -4px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

.booking-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

/* Enhanced Quantity Selector */
.quantity-selector {
  display: flex;
  align-items: center;
  border: 2px solid rgba(148, 163, 184, 0.2);
  border-radius: 14px;
  overflow: hidden;
  background: rgba(248, 250, 252, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(4px);
  position: relative;
}

.quantity-selector::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 12px;
  padding: 1px;
  background: linear-gradient(135deg, transparent, rgba(59, 130, 246, 0.1));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quantity-selector:focus-within {
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.12),
    0 8px 16px -4px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.quantity-selector:focus-within::before {
  opacity: 1;
}

.quantity-btn {
  background: transparent;
  border: none;
  padding: 1rem 1.25rem;
  font-size: 1.25rem;
  font-weight: 800;
  color: #475569;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 12px;
}

.quantity-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(139, 92, 246, 0.1));
  color: #3b82f6;
  transform: scale(1.05);
}

.quantity-btn:active:not(:disabled) {
  transform: scale(0.95);
}

.quantity-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.quantity-input {
  border: none;
  text-align: center;
  font-weight: 800;
  font-size: 1.125rem;
  padding: 1rem 0.75rem;
  width: 80px;
  background: transparent;
  color: #1e293b;
  transition: all 0.2s ease;
}

.quantity-input:focus {
  outline: none;
  box-shadow: none;
  color: #3b82f6;
}

.age-info {
  font-size: 0.8125rem;
  color: #64748b;
  margin-top: 0.625rem;
  font-weight: 500;
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.age-info::before {
  content: 'ℹ';
  font-size: 0.75rem;
  color: #3b82f6;
  opacity: 0.6;
}

/* Enhanced Guest Names Section */
.guest-names {
  margin-top: 0.75rem;
}

.guest-name-inputs {
  display: flex;
  flex-direction: column;
  gap: 0.875rem;
}

.guest-name-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  padding: 0.5rem;
  background: rgba(248, 250, 252, 0.5);
  border-radius: 12px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  transition: all 0.3s ease;
}

.guest-name-row:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.guest-title-select {
  flex: 0 0 auto;
  width: 80px;
  padding: 0.75rem 0.625rem;
  border: 1.5px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.9);
  color: #475569;
  transition: all 0.3s ease;
}

.guest-name-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1.5px solid rgba(148, 163, 184, 0.2);
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  color: #1e293b;
  transition: all 0.3s ease;
}

.guest-name-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.guest-name-input:focus,
.guest-title-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 1);
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.12),
    0 4px 8px -2px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Enhanced Submit Button */
.booking-submit-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #8b5cf6 100%);
  color: white;
  border: none;
  padding: 1.375rem 2.5rem;
  border-radius: 16px;
  font-size: 1.0625rem;
  font-weight: 800;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-top: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.075em;
  width: 100%;
  box-shadow:
    0 8px 20px -4px rgba(59, 130, 246, 0.4),
    0 4px 8px -2px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.booking-submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.booking-submit-btn:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 50%, #7c3aed 100%);
  transform: translateY(-3px);
  box-shadow:
    0 12px 30px -4px rgba(59, 130, 246, 0.5),
    0 8px 16px -2px rgba(139, 92, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.booking-submit-btn:hover::before {
  left: 100%;
}

.booking-submit-btn:active {
  transform: translateY(-1px);
  box-shadow:
    0 6px 16px -4px rgba(59, 130, 246, 0.4),
    0 4px 8px -2px rgba(139, 92, 246, 0.2);
}

.booking-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 8px -2px rgba(59, 130, 246, 0.2);
}

/* Enhanced Footer */
.booking-footer {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 2px solid rgba(59, 130, 246, 0.1);
  text-align: center;
  position: relative;
}

.booking-footer::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

.organized-by {
  font-size: 0.8125rem;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 600;
  opacity: 0.8;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.organized-by::before {
  content: '✨';
  font-size: 0.875rem;
  opacity: 0.6;
}

/* Enhanced UI Elements */
.field-error {
  font-size: 0.8125rem;
  color: #ef4444;
  margin-top: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border-radius: 8px;
  border-left: 3px solid #ef4444;
  display: none;
  animation: slideDown 0.3s ease;
}

.field-error.show {
  display: block;
}

.booking-field.error .booking-input,
.booking-field.error .booking-select {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
}

.booking-field.error .booking-input:focus,
.booking-field.error .booking-select:focus {
  border-color: #ef4444;
  box-shadow:
    0 0 0 4px rgba(239, 68, 68, 0.12),
    0 8px 16px -4px rgba(239, 68, 68, 0.15);
}

.booking-field.success .booking-input,
.booking-field.success .booking-select {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.05);
}

.booking-field.success .booking-input:focus,
.booking-field.success .booking-select:focus {
  border-color: #10b981;
  box-shadow:
    0 0 0 4px rgba(16, 185, 129, 0.12),
    0 8px 16px -4px rgba(16, 185, 129, 0.15);
}

/* Loading Spinner */
.loading-spinner {
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Button States */
.booking-submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.booking-submit-btn .btn-text,
.booking-submit-btn .btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.booking-submit-btn.loading .btn-text {
  display: none;
}

.booking-submit-btn.loading .btn-loading {
  display: flex;
}

/* Form Messages */
.form-message {
  margin-top: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 10px;
  font-size: 0.875rem;
  font-weight: 500;
  display: none;
  animation: slideDown 0.3s ease;
}

.form-message.success {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
  display: block;
}

.form-message.error {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
  display: block;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Focus Indicators */
.booking-field:focus-within .booking-label {
  color: #3b82f6;
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Hover Effects */
.booking-field:hover .booking-input:not(:focus),
.booking-field:hover .booking-select:not(:focus) {
  border-color: rgba(59, 130, 246, 0.3);
  background: rgba(255, 255, 255, 0.9);
}

/* Professional Enhancements */
.booking-form-wrapper::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
  pointer-events: none;
  z-index: -1;
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced focus states */
.booking-input:focus,
.booking-select:focus,
.guest-name-input:focus,
.guest-title-select:focus {
  outline: none;
  border-color: #3b82f6;
  background: rgba(255, 255, 255, 0.98);
  box-shadow:
    0 0 0 4px rgba(59, 130, 246, 0.12),
    0 8px 16px -4px rgba(59, 130, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  transform: translateY(-1px);
}

/* Micro-interactions */
.booking-label {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.booking-field:focus-within .booking-label {
  color: #3b82f6;
  transform: translateY(-1px);
}

/* Enhanced button interactions */
.booking-submit-btn {
  position: relative;
  overflow: hidden;
}

.booking-submit-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.booking-submit-btn:active::after {
  width: 300px;
  height: 300px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .booking-form-wrapper {
    border: 2px solid;
  }

  .booking-input,
  .booking-select {
    border-width: 2px;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .booking-form-wrapper {
    background: linear-gradient(145deg, #1f2937 0%, #111827 100%);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .booking-input,
  .booking-select {
    background: rgba(31, 41, 55, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: #f9fafb;
  }

  .booking-label {
    color: #f3f4f6;
  }

  .price-amount {
    color: #f9fafb;
  }
}

/* Tour Information Section Styles */
.tour-info-section {
  margin-top: 3rem;
  padding: 2rem 0;
}

.tour-section-title {
  font-size: 1.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 1.5rem;
  position: relative;
  padding-left: 1rem;
}

.tour-section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
}

/* Tour Highlights */
.tour-highlights {
  margin-bottom: 3rem;
}

.highlights-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: 1fr;
}

.highlight-item {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.highlight-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 24px -8px rgba(59, 130, 246, 0.15),
    0 8px 16px -4px rgba(0, 0, 0, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

.highlight-item:hover::before {
  opacity: 1;
}

.highlight-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.3s ease;
}

.highlight-item:hover .highlight-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 16px -4px rgba(59, 130, 246, 0.4);
}

.highlight-content h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.highlight-content p {
  font-size: 0.9375rem;
  color: #64748b;
  line-height: 1.5;
  margin: 0;
}

/* What's Included Section */
.tour-included {
  background: linear-gradient(145deg, #f8fafc 0%, #ffffff 100%);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.tour-included::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  border-radius: 20px 20px 0 0;
}

.included-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.included-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(16, 185, 129, 0.1);
  transition: all 0.3s ease;
  font-size: 0.9375rem;
  font-weight: 500;
  color: #374151;
}

.included-item:hover {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
  transform: translateX(4px);
}

.included-icon {
  color: #10b981;
  flex-shrink: 0;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 50%;
  padding: 2px;
}

/* Responsive Design for Tour Info */
@media screen and (min-width: 750px) {
  .highlights-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tour-info-section {
    margin-top: 4rem;
    padding: 2.5rem 0;
  }
}

@media screen and (min-width: 990px) {
  .tour-info-section {
    margin-top: 2rem;
    padding: 2rem;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 20px;
    border: 1px solid rgba(148, 163, 184, 0.1);
    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.05);
  }
}

@media screen and (max-width: 749px) {
  .tour-info-section {
    margin-top: 2rem;
    padding: 1.5rem 1rem;
  }

  .highlight-item {
    padding: 1.25rem;
  }

  .highlight-icon {
    width: 40px;
    height: 40px;
  }

  .tour-included {
    padding: 1.5rem;
  }
}

/* Tour Itinerary Section */
.tour-itinerary {
  margin-top: 3rem;
}

.itinerary-timeline {
  position: relative;
  padding-left: 2rem;
}

.itinerary-timeline::before {
  content: '';
  position: absolute;
  left: 1.5rem;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6, #06b6d4);
  border-radius: 2px;
}

.itinerary-day {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.itinerary-day:last-child {
  margin-bottom: 0;
}

.day-number {
  position: absolute;
  left: -2rem;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 800;
  font-size: 1.125rem;
  box-shadow: 0 4px 12px -2px rgba(59, 130, 246, 0.4);
  z-index: 2;
  transition: all 0.3s ease;
}

.itinerary-day:hover .day-number {
  transform: scale(1.1);
  box-shadow: 0 8px 20px -4px rgba(59, 130, 246, 0.5);
}

.day-content {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  padding: 1.5rem;
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.1);
  flex: 1;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.day-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.itinerary-day:hover .day-content {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

.itinerary-day:hover .day-content::before {
  opacity: 1;
}

.day-content h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.75rem;
  line-height: 1.3;
}

.day-content p {
  font-size: 0.9375rem;
  color: #64748b;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design for Itinerary */
@media screen and (max-width: 749px) {
  .itinerary-timeline {
    padding-left: 1.5rem;
  }

  .itinerary-timeline::before {
    left: 1rem;
  }

  .day-number {
    left: -1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1rem;
  }

  .day-content {
    padding: 1.25rem;
  }

  .tour-itinerary {
    margin-top: 2rem;
  }
}

/* Enhanced Responsive Design */
@media screen and (max-width: 1200px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2.5rem;
  }

  .booking-form-wrapper {
    max-width: 100%;
    margin-left: 0;
    position: static;
  }
}

@media screen and (max-width: 990px) {
  .product {
    grid-template-columns: 1fr !important;
  }

  .product__booking-wrapper {
    order: 3;
    margin-top: 3rem;
  }

  .booking-form-wrapper {
    max-width: 450px;
    margin: 0 auto;
    border-radius: 18px;
  }

  .booking-form-container {
    padding: 2.25rem 1.75rem;
  }
}

@media screen and (max-width: 750px) {
  .booking-form-container {
    padding: 2rem 1.5rem;
  }

  .price-amount {
    font-size: 2rem;
  }

  .booking-form {
    gap: 1.5rem;
  }

  .booking-field {
    gap: 0.75rem;
  }

  .guest-name-row {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem;
  }

  .guest-title-select {
    width: 100%;
    padding: 0.875rem 1rem;
  }

  .guest-name-input {
    padding: 0.875rem 1rem;
  }

  .quantity-btn {
    padding: 0.875rem 1rem;
    min-width: 44px;
  }

  .quantity-input {
    width: 70px;
    padding: 0.875rem 0.5rem;
  }
}

/* Mobile Layout */
@media screen and (max-width: 600px) {
  .product__booking-wrapper {
    position: static;
    margin-top: 2rem;
  }

  .booking-form-wrapper {
    max-width: 100%;
    margin: 0;
    border-radius: 16px;
    box-shadow:
      0 15px 30px -8px rgba(0, 0, 0, 0.1),
      0 6px 12px -2px rgba(0, 0, 0, 0.08);
  }

  .booking-form-container {
    padding: 1.75rem 1.25rem;
  }

  .booking-form-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
  }

  .price-amount {
    font-size: 1.875rem;
  }

  .booking-submit-btn {
    padding: 1.25rem 2rem;
    font-size: 1rem;
  }
}

/* Enhanced Responsive Layout */
@media screen and (min-width: 750px) {
  .product-with-booking {
    grid-template-columns: 1fr 400px;
    gap: 3rem;
    align-items: start;
  }

  .booking-form-wrapper {
    max-width: 400px;
    position: sticky;
    top: 2rem;
  }

  .booking-form-container {
    padding: 2.5rem 2rem;
  }
}

@media screen and (min-width: 990px) {
  .product-with-booking {
    grid-template-columns: 1fr 420px;
    gap: 3.5rem;
  }

  .booking-form-wrapper {
    max-width: 420px;
  }

  .booking-form-container {
    padding: 2.75rem 2.25rem;
  }
}

@media screen and (min-width: 1200px) {
  .product-with-booking {
    grid-template-columns: 1fr 440px;
    gap: 4rem;
  }

  .booking-form-wrapper {
    max-width: 440px;
  }

  .booking-form-container {
    padding: 3rem 2.5rem;
  }
}

@media screen and (min-width: 1400px) {
  .product-with-booking {
    grid-template-columns: 1fr 460px;
    gap: 4.5rem;
  }

  .booking-form-wrapper {
    max-width: 460px;
  }
}


