/* Tour Information Section Styles */
.tour-info-section {
  margin: 2rem 0;
  padding: 2rem;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.tour-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1.5rem;
  text-align: center;
}

/* Tour Highlights */
.tour-highlights {
  margin-bottom: 3rem;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.highlight-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.highlight-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.highlight-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.highlight-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.highlight-content p {
  font-size: 0.9rem;
  color: #718096;
  line-height: 1.5;
  margin: 0;
}

/* What's Included Section */
.tour-included {
  margin-bottom: 3rem;
}

.included-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.included-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f0fff4;
  border-radius: 8px;
  border: 1px solid #c6f6d5;
}

.included-icon {
  color: #38a169;
  flex-shrink: 0;
}

.included-item span {
  font-size: 0.9rem;
  color: #2d3748;
  font-weight: 500;
}

/* Tour Itinerary */
.tour-itinerary {
  margin-bottom: 2rem;
}

.itinerary-timeline {
  position: relative;
  margin-top: 2rem;
}

.itinerary-timeline::before {
  content: '';
  position: absolute;
  left: 24px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #667eea, #764ba2);
  border-radius: 1px;
}

.itinerary-day {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
  margin-bottom: 2rem;
  padding-left: 0;
}

.day-number {
  position: relative;
  z-index: 2;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.day-content {
  flex: 1;
  background: #ffffff;
  padding: 1.5rem;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-top: 0.5rem;
}

.day-content h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.75rem;
}

.day-content p {
  font-size: 0.9rem;
  color: #718096;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tour-info-section {
    padding: 1.5rem;
    margin: 1rem 0;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .highlight-item {
    padding: 1rem;
  }
  
  .included-list {
    grid-template-columns: 1fr;
  }
  
  .itinerary-timeline::before {
    left: 20px;
  }
  
  .day-number {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .day-content {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .tour-section-title {
    font-size: 1.3rem;
  }
  
  .highlight-item {
    flex-direction: column;
    text-align: center;
  }
  
  .highlight-icon {
    align-self: center;
  }
}
